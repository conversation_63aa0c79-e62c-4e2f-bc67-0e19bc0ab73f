---
import Layout from '@/layouts/Layout.astro';
import { isUserAuthenticated, getCurrentUser } from '@/middleware/auth';

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);
const user = getCurrentUser(Astro.locals);
---

<Layout title="Админ панель">
  <div class="container mx-auto p-6 max-w-2xl">
    <h1 class="text-3xl font-bold mb-6">Админ панель</h1>

    {isAuthenticated ? (
      <div class="bg-green-50 border border-green-200 rounded-lg p-6">
        <h2 class="text-xl font-semibold text-green-800 mb-4">
          ✅ Вы авторизованы
        </h2>
        <div class="space-y-2 text-green-700">
          <p><strong>ID:</strong> {user?.id}</p>
          <p><strong>Email:</strong> {user?.email}</p>
          <p><strong>Тип:</strong> {user?.type === 'admin' ? 'Администратор' : 'Пользователь'}</p>
          <p><strong>Токен:</strong> {user?.token ? '***' + user.token.slice(-10) : 'Не найден'}</p>
        </div>

        <div class="mt-6">
          <h3 class="text-lg font-medium text-green-800 mb-3">Доступные действия:</h3>
          <div class="space-y-2">
            <a
              href="/admin/edit/pages/do0q1anuy7nzxdx"
              class="inline-block px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
            >
              Редактировать страницу "О нас"
            </a>
          </div>
        </div>
      </div>
    ) : (
      <div class="bg-red-50 border border-red-200 rounded-lg p-6">
        <h2 class="text-xl font-semibold text-red-800 mb-4">
          ❌ Вы не авторизованы
        </h2>
        <p class="text-red-700 mb-4">
          Для доступа к админ панели необходимо войти в систему.
        </p>

        <div class="space-y-4">
          <div class="bg-yellow-50 border border-yellow-200 rounded p-4">
            <h3 class="font-medium text-yellow-800 mb-2">Как авторизоваться:</h3>
            <ol class="list-decimal list-inside text-yellow-700 space-y-1">
              <li>Перейдите на любую страницу редактирования (например, <a href="/admin/edit/pages/do0q1anuy7nzxdx" class="underline">эту</a>)</li>
              <li>Введите логин и пароль администратора</li>
              <li>После успешной авторизации вернитесь на эту страницу</li>
            </ol>
          </div>

          <button
            onclick="window.location.reload()"
            class="px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700 transition-colors"
          >
            Обновить страницу
          </button>
        </div>
      </div>
    )}

    <div class="mt-8 p-4 bg-gray-50 border border-gray-200 rounded-lg">
      <h3 class="font-medium text-gray-800 mb-2">Отладочная информация:</h3>
      <div class="text-sm text-gray-600 space-y-1">
        <p><strong>Astro.locals.isAuthenticated:</strong> {Astro.locals.isAuthenticated}</p>
        <p><strong>Astro.locals.user:</strong> {Astro.locals.user ? 'Объект найден' : 'null'}</p>
        <p><strong>Время проверки:</strong> {new Date().toLocaleString('ru-RU')}</p>
      </div>
    </div>
  </div>
</Layout>
