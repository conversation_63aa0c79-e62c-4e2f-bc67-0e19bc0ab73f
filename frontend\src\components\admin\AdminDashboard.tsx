import * as React from 'react'
import { AdminAuth } from './AdminAuth'
import { AdminNotifications, useAdminNotifications } from './AdminNotifications'
import { CallbackRequestsWidget } from './CallbackRequestsWidget'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { getCollectionsStats, logout } from '@/lib/pocketbase-admin'
import { Users, FileText, Tag, MessageSquare, HelpCircle, Newspaper, DollarSign, Settings, LogOut, Edit, Plus, BarChart3, Code, UserCheck, Shield, Phone, FolderOpen, Grid3X3 } from 'lucide-react'
import { navigate } from 'astro:transitions/client'

interface AdminDashboardProps {
  isAuthenticated: boolean
  pbUrl: string
}

interface CollectionStats {
  doctors: number
  services: number
  promos: number
  reviews: number
  faq: number
  news: number
  pages: number
  prices: number
  html_blocks: number
  personal: number
  callback_requests: number
  users: number
  service_categories: number
  documents: number
  files: number
}

export const AdminDashboard: React.FC<AdminDashboardProps> = ({ isAuthenticated: serverAuth, pbUrl }) => {
  const [isAuthenticated, setIsAuthenticated] = React.useState(serverAuth)
  const [loading, setLoading] = React.useState(false)
  const [stats, setStats] = React.useState<CollectionStats | null>(null)
  const [token, setToken] = React.useState<string>('')
  const notifications = useAdminNotifications()

  React.useEffect(() => {
    const savedToken = localStorage.getItem('pb_token')
    if (savedToken) {
      setToken(savedToken)
      setIsAuthenticated(true)
      loadStats(savedToken)
    }
  }, [])

  const loadStats = async (authToken: string) => {
    setLoading(true)
    try {
      const statsData = await getCollectionsStats(authToken)
      setStats(statsData as any as CollectionStats)
    } catch (error) {
      console.error('Error loading stats:', error)
      notifications.error('Ошибка загрузки статистики', 'Не удалось получить данные о коллекциях')
    } finally {
      setLoading(false)
    }
  }

  const handleAuth = (authToken: string) => {
    setToken(authToken)
    setIsAuthenticated(true)
    loadStats(authToken)
    notifications.success('Авторизация успешна', 'Добро пожаловать в админ-панель!')
  }

  const handleLogout = () => {
    logout()
    setToken('')
    setIsAuthenticated(false)
    setStats(null)
    notifications.info('Выход выполнен', 'До свидания!')
  }

  if (!isAuthenticated) {
    return <AdminAuth onAuth={handleAuth} pbUrl={pbUrl} />
  }

  const collections = [
    {
      name: 'doctors',
      title: 'Врачи и специалисты',
      description: 'Профили врачей, специализации, опыт работы',
      icon: Users,
      color: 'bg-blue-500',
      count: stats?.doctors || 0
    },
    {
      name: 'services',
      title: 'Медицинские услуги',
      description: 'Каталог всех услуг клиники с описаниями',
      icon: FileText,
      color: 'bg-green-500',
      count: stats?.services || 0
    },
    {
      name: 'promos',
      title: 'Акции и скидки',
      description: 'Специальные предложения и акционные программы',
      icon: Tag,
      color: 'bg-orange-500',
      count: stats?.promos || 0
    },
    {
      name: 'reviews',
      title: 'Отзывы пациентов',
      description: 'Отзывы и рекомендации от клиентов',
      icon: MessageSquare,
      color: 'bg-purple-500',
      count: stats?.reviews || 0
    },
    {
      name: 'faq',
      title: 'Вопросы и ответы',
      description: 'Часто задаваемые вопросы пациентов',
      icon: HelpCircle,
      color: 'bg-yellow-500',
      count: stats?.faq || 0
    },
    {
      name: 'news',
      title: 'Новости клиники',
      description: 'Новости, статьи и полезная информация',
      icon: Newspaper,
      color: 'bg-red-500',
      count: stats?.news || 0
    },
    {
      name: 'pages',
      title: 'Страницы сайта',
      description: 'Статические страницы: О нас, Контакты и др.',
      icon: FileText,
      color: 'bg-indigo-500',
      count: stats?.pages || 0
    },
    {
      name: 'prices',
      title: 'Прайс-лист услуг',
      description: 'Цены на все медицинские услуги',
      icon: DollarSign,
      color: 'bg-emerald-500',
      count: stats?.prices || 0
    },
    {
      name: 'html_blocks',
      title: 'Контентные блоки',
      description: 'HTML-блоки для настройки страниц',
      icon: Code,
      color: 'bg-cyan-500',
      count: stats?.html_blocks || 0
    },
    {
      name: 'personal',
      title: 'Персонал клиники',
      description: 'Сотрудники: администраторы, медсестры и др.',
      icon: UserCheck,
      color: 'bg-teal-500',
      count: stats?.personal || 0
    },
    {
      name: 'callback_requests',
      title: 'Заявки на обратный звонок',
      description: 'Запросы клиентов на обратную связь',
      icon: Phone,
      color: 'bg-red-500',
      count: stats?.callback_requests || 0
    },
    {
      name: 'users',
      title: 'Пользователи сайта',
      description: 'Зарегистрированные пользователи',
      icon: Users,
      color: 'bg-gray-500',
      count: stats?.users || 0
    },
    {
      name: 'service_categories',
      title: 'Категории услуг',
      description: 'Группировка услуг по направлениям',
      icon: Grid3X3,
      color: 'bg-pink-500',
      count: stats?.service_categories || 0
    },
    {
      name: 'files',
      title: 'Документы и сертификаты',
      description: 'Лицензии, сертификаты врачей, документы',
      icon: FolderOpen,
      color: 'bg-amber-500',
      count: stats?.files || 0
    },
    {
      name: 'documents',
      title: 'Документы',
      description: 'Документы медицинской организации',
      icon: Shield,
      color: 'bg-blue-600',
      count: stats?.documents || 0
    }
  ]

  return (
    <div className='min-h-screen bg-gradient-to-br from-[#8BC34A]/10 via-white to-[#8BC34A]/5'>
      {/* Header */}
      <header className='border-b border-gray-200 bg-white shadow-sm'>
        <div className='mx-auto max-w-7xl px-4 sm:px-6 lg:px-8'>
          <div className='flex h-16 items-center justify-between'>
            <div className='flex items-center gap-3'>
              <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-gradient-to-br from-[#8BC34A] to-[#4E8C29]'>
                <Settings className='h-5 w-5 text-white' />
              </div>
              <div>
                <h1 className='text-xl font-bold text-gray-900'>Панель администратора</h1>
                <p className='text-sm text-gray-500'>Стом-Лайн</p>
              </div>
            </div>
            <Button onClick={handleLogout} variant='outline' size='sm' className='border-red-200 text-red-600 hover:bg-red-50'>
              <LogOut className='mr-2 h-4 w-4' />
              Выйти
            </Button>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className='mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8'>
        {/* Stats Overview */}
        <div className='mb-8'>
          <h2 className='mb-4 text-2xl font-bold text-gray-900'>Обзор контента</h2>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-4'>
            {collections.map((collection) => (
              <Card key={collection.name} className='transition-shadow duration-200 hover:shadow-lg'>
                <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
                  <CardTitle className='text-sm font-medium text-gray-600'>{collection.title}</CardTitle>
                  <div className={`h-8 w-8 ${collection.color} flex items-center justify-center rounded-lg`}>
                    <collection.icon className='h-4 w-4 text-white' />
                  </div>
                </CardHeader>
                <CardContent>
                  <div className='text-2xl font-bold text-gray-900'>{loading ? '...' : collection.count}</div>
                  <p className='mt-1 text-xs text-gray-500'>{collection.description}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Collections Management */}
        <div>
          <h2 className='mb-4 text-2xl font-bold text-gray-900'>Управление контентом</h2>
          <div className='grid grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
            {collections.map((collection) => (
              <Card key={collection.name} className='transition-shadow duration-200 hover:shadow-lg'>
                <CardHeader>
                  <div className='flex items-center gap-3'>
                    <div className={`h-10 w-10 ${collection.color} flex items-center justify-center rounded-lg`}>
                      <collection.icon className='h-5 w-5 text-white' />
                    </div>
                    <div>
                      <CardTitle className='text-lg'>{collection.title}</CardTitle>
                      <CardDescription>{collection.description}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className='space-y-3'>
                  <div className='flex items-center justify-between'>
                    <span className='text-sm text-gray-600'>Записей:</span>
                    <Badge variant='secondary'>{loading ? '...' : collection.count}</Badge>
                  </div>
                  <div className='flex gap-2'>
                    <Button
                      size='sm'
                      className='flex-1 bg-[#8BC34A] hover:bg-[#4E8C29]'
                      onClick={() => {
                        navigate(`/admin/${collection.name}`)
                      }}
                    >
                      <Edit className='mr-1 h-4 w-4' />
                      Управлять
                    </Button>
                    <Button
                      size='sm'
                      variant='outline'
                      className='border-[#8BC34A] text-[#8BC34A] hover:bg-[#8BC34A]/10'
                      onClick={() => {
                        navigate(`/admin/create/${collection.name}`)
                      }}
                    >
                      <Plus className='h-4 w-4' />
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Callback Requests Widget */}
        <div className='mt-8'>
          <CallbackRequestsWidget pbUrl='https://pb.stom-line.ru' />
        </div>

        {/* Quick Actions */}
        <div className='mt-8'>
          <h2 className='mb-4 text-2xl font-bold text-gray-900'>Быстрые действия</h2>
          <div className='grid grid-cols-1 gap-4 md:grid-cols-4'>
            <Button
              className='h-16 justify-start bg-[#8BC34A] text-left hover:bg-[#4E8C29]'
              onClick={() => navigate('/admin/create')}
            >
              <Plus className='mr-3 h-6 w-6' />
              <div>
                <div className='font-medium'>Создать запись</div>
                <div className='text-sm opacity-90'>Добавить новый контент</div>
              </div>
            </Button>
            <Button
              variant='outline'
              className='h-16 justify-start border-[#8BC34A] text-left text-[#8BC34A] hover:bg-[#8BC34A]/10'
              onClick={() => window.open('https://pb.stom-line.ru/_/', '_blank')}
            >
              <Settings className='mr-3 h-6 w-6' />
              <div>
                <div className='font-medium'>PocketBase Admin</div>
                <div className='text-sm opacity-70'>Полная админ-панель</div>
              </div>
            </Button>
            <Button
              variant='outline'
              className='h-16 justify-start border-[#8BC34A] text-left text-[#8BC34A] hover:bg-[#8BC34A]/10'
              onClick={() => window.open('/', '_blank')}
            >
              <BarChart3 className='mr-3 h-6 w-6' />
              <div>
                <div className='font-medium'>Просмотр сайта</div>
                <div className='text-sm opacity-70'>Открыть главную страницу</div>
              </div>
            </Button>
            <Button
              variant='outline'
              className='h-16 justify-start border-gray-300 text-left text-gray-700 hover:bg-gray-50'
              onClick={() => window.location.reload()}
            >
              <Settings className='mr-3 h-6 w-6' />
              <div>
                <div className='font-medium'>Обновить данные</div>
                <div className='text-sm opacity-70'>Перезагрузить статистику</div>
              </div>
            </Button>
          </div>
        </div>
      </main>

      {/* Notifications */}
      <AdminNotifications notifications={notifications.notifications} onRemove={notifications.removeNotification} />
    </div>
  )
}
