---
import Layout from '../../layouts/Layout.astro';
import PocketBase from 'pocketbase';
import { Button } from '../../components/ui/button';
import { CalendarIcon, PhoneIcon, MapPinIcon } from 'lucide-react';
import { generateDoctorJsonLd } from '../../lib/seo-config';
import RDFaMarkup from '../../components/RDFaMarkup.astro';

// URL API
const PUBLIC_API_URL = 'https://pb.stom-line.ru';

export async function getStaticPaths() {
  const pb = new PocketBase(PUBLIC_API_URL);

  try {
    const doctors = await pb.collection('doctors').getFullList({
      expand: 'specializations,services,certificates',
    });

    return doctors.map((doctor) => ({
      params: { slug: doctor.slug || doctor.id },
      props: { doctor },
    }));
  } catch (error) {
    console.error('Ошибка при получении данных о специалистах:', error);
    return [];
  }
}

const { doctor } = Astro.props;

// Проверяем, что doctor определен
if (!doctor) {
  return Astro.redirect('/specialists');
}

// Получаем URL фото, если есть
let photoUrl = '';
if (doctor?.photo) {
  try {
    photoUrl = `${PUBLIC_API_URL}/api/files/${doctor.collectionId || 'pbc_doctors'}/${doctor.id}/${doctor.photo}`;
  } catch (error) {
    console.error('Ошибка при получении URL фото:', error);
  }
}

// Получаем специализации и услуги
const specializations = doctor?.expand?.specializations || [];
const services = doctor?.expand?.services || [];

// Форматируем полное имя
const surname = doctor?.surname || '';
const name = doctor?.name || '';
const patronymic = doctor?.patronymic || '';
const fullName = `${surname} ${name} ${patronymic}`.trim() || 'Специалист';

// Мета-данные
const position = doctor?.position || 'Врач';
const metaTitle = doctor?.meta_title || `${fullName} - ${position} | Стоматология STOM-LINE в Мурманске`;
const metaDescription = doctor?.meta_description || `${position} ${fullName} в стоматологической клинике STOM-LINE. ${doctor?.short_description?.replace(/<[^>]*>/g, '') || 'Опытный специалист с современными методиками лечения.'} Записаться на прием: +7 (8152) 52-57-08`;

// Генерируем структурированные данные для врача
const doctorJsonLd = generateDoctorJsonLd(doctor);

// Формируем ключевые слова
const keywords = [
  fullName,
  position,
  'стоматолог Мурманск',
  'врач стоматолог',
  'стоматология STOM-LINE',
  ...specializations.map((spec: any) => spec.name)
];

// URL изображения для Open Graph
const ogImage = photoUrl || 'https://stom-line.ru/og-image.jpg';
---

<Layout
  title={metaTitle}
  description={metaDescription}
  keywords={keywords}
  image={ogImage}
  type="profile"
  jsonLd={doctorJsonLd}
>
  <div class="container mx-auto px-4 py-12">
    <div class="max-w-5xl mx-auto">
      <!-- Хлебные крошки -->
      <div class="text-sm text-olive-500 mb-6">
        <a href="/" class="hover:text-olive-700">Главная</a>
        <span class="mx-2">/</span>
        <a href="/specialists" class="hover:text-olive-700">Специалисты</a>
        <span class="mx-2">/</span>
        <span class="text-olive-700">{fullName}</span>
        <a href={`/admin/edit/doctors/${doctor.id}`} target="_blank" rel="noopener noreferrer" class="ml-4 text-xs text-blue-600 underline">Редактировать</a>
      </div>

      <!-- Основная информация о враче -->
      <div class="bg-white rounded-xl shadow-md overflow-hidden mb-10">
        <div class="md:flex">
          <div class="md:flex-shrink-0">
            {photoUrl ? (
              <img
                src={photoUrl}
                alt={fullName}
                class="h-64 w-full object-cover md:w-64 md:h-auto"
              />
            ) : (
              <div class="h-64 w-full md:w-64 md:h-auto bg-olive-100 flex items-center justify-center">
                <span class="text-olive-400 text-lg">Фото отсутствует</span>
              </div>
            )}
          </div>

          <div class="p-8">
            <h1 class="text-3xl font-bold text-olive-800 mb-2">{fullName}</h1>

            {doctor.position && (
              <p class="text-xl text-olive-600 mb-4">{doctor.position}</p>
            )}

            {doctor.short_description && (
              <div class="text-olive-700 mb-6" set:html={doctor.short_description} />
            )}

            <div class="flex flex-wrap gap-2 mb-6">
              {specializations.map((spec) => (
                <span class="bg-olive-100 text-olive-700 px-3 py-1 rounded-full text-sm">
                  {spec.name}
                </span>
              ))}
            </div>

            <div class="flex flex-col sm:flex-row gap-4">
              <Button className="bg-olive-600 hover:bg-olive-700 text-white flex items-center gap-2">
                <PhoneIcon className="h-4 w-4" />
                <a href="tel:+78152525708" class="inline-block">Записаться на прием</a>
              </Button>

              <Button variant="outline" className="border-olive-600 text-olive-700 hover:bg-olive-50 flex items-center gap-2">
                <CalendarIcon className="h-4 w-4" />
                <span>Пн-Пт: 9:00-20:00, Сб: 9:00-18:00</span>
              </Button>
            </div>
          </div>
        </div>
      </div>

      <!-- Биография и опыт -->
      {doctor.biography && (
        <div class="bg-white rounded-xl shadow-md overflow-hidden p-8 mb-10">
          <h2 class="text-2xl font-semibold text-olive-800 mb-4">Образование и опыт</h2>
          <div class="prose prose-olive max-w-none" set:html={doctor.biography} />
        </div>
      )}

      <!-- Услуги специалиста -->
      {services.length > 0 && (
        <div class="bg-white rounded-xl shadow-md overflow-hidden p-8 mb-10">
          <h2 class="text-2xl font-semibold text-olive-800 mb-4">Услуги специалиста</h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            {services.map((service) => (
              <a
                href={`/services/${service.slug || service.id}`}
                class="p-4 border border-olive-100 rounded-lg hover:bg-olive-50 transition-colors"
              >
                <h3 class="font-medium text-olive-800">{service.name}</h3>
                {service.short_description && (
                  <p class="mt-1 text-sm text-olive-600 line-clamp-2">{service.short_description}</p>
                )}
              </a>
            ))}
          </div>
        </div>
      )}

      <!-- Контактная информация -->
      <div class="bg-olive-50 rounded-xl shadow-sm overflow-hidden p-8">
        <h2 class="text-2xl font-semibold text-olive-800 mb-4">Контактная информация</h2>

        <div class="flex flex-col gap-4">
          <div class="flex items-start gap-3">
            <PhoneIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Телефон для записи</p>
              <a href="tel:+78152525708" class="text-olive-600 hover:text-olive-800">+7 (8152) 52-57-08</a>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <MapPinIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Адрес клиники</p>
              <p class="text-olive-600">г. Мурманск, ул. Полярные Зори, д. 35/2</p>
            </div>
          </div>

          <div class="flex items-start gap-3">
            <CalendarIcon className="h-5 w-5 text-olive-600 mt-0.5" />
            <div>
              <p class="font-medium text-olive-800">Часы работы</p>
              <p class="text-olive-600">Пн-Пт: 9:00-20:00, Сб: 9:00-18:00, Вс: выходной</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- RDFa разметка для врача -->
  <RDFaMarkup type="doctor" data={doctor} />
</Layout>
