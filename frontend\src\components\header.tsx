import { useEffect, useState } from 'react'
import { Menu, Phone, Search as SearchIcon } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerFooter,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from '@/components/ui/drawer'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog'
import { Search } from '@/components/search'
import { AccessibilityToggle } from '@/components/accessibility/AccessibilityToggle'
import { CallbackForm } from '@/components/callback-form'

interface NavItem {
  title: string
  href: string
}

export default function Header() {
  // Состояние для отслеживания скролла страницы
  // const [scrolled, setScrolled] = useState(true)

  // Состояние для контроля drawer
  const [drawerOpen, setDrawerOpen] = useState(false)

  // Состояние для контроля диалога формы обратной связи
  const [callbackDialogOpen, setCallbackDialogOpen] = useState(false)

  const navItems: NavItem[] = [
    { title: 'Главная', href: '/' },
    { title: 'Услуги', href: '/services' },
    { title: 'Цены', href: '/prices' },
    { title: 'Специалисты', href: '/specialists' },
    { title: 'Акции', href: '/promos' },
    { title: 'Новости', href: '/news' },
    { title: 'Отзывы', href: '/reviews' },
    { title: 'FAQ', href: '/faq' },
    { title: 'Документы', href: '/documents' },
    { title: 'О нас', href: '/about' },
    { title: 'Контакты', href: '/contacts' }
  ]

  useEffect(() => {
    // Функционал скролла отключен, так как не используется в текущей версии
    // const handleScroll = () => {
    //   setScrolled(window.scrollY > 20)
    // }
    // window.addEventListener('scroll', handleScroll)
    // return () => window.removeEventListener('scroll', handleScroll)

    // Закрываем drawer при навигации
    const closeDrawerOnNavigation = () => {
      setDrawerOpen(false)
    }

    // Обработчики для различных типов навигации

    // 1. Astro transitions (основной способ навигации в Astro)
    window.addEventListener('astro:before-preparation', closeDrawerOnNavigation)
    window.addEventListener('astro:after-swap', closeDrawerOnNavigation)

    // 2. Swup навигация (если используется)
    window.addEventListener('swup:contentReplaced', closeDrawerOnNavigation)
    window.addEventListener('swup:pageView', closeDrawerOnNavigation)

    // 3. Стандартная навигация браузера
    const origPush = history.pushState
    history.pushState = function (...args) {
      origPush.apply(this, args)
      closeDrawerOnNavigation()
    }
    const origReplace = history.replaceState
    history.replaceState = function (...args) {
      origReplace.apply(this, args)
      closeDrawerOnNavigation()
    }

    // 4. Навигация назад/вперед
    window.addEventListener('popstate', closeDrawerOnNavigation)

    // 5. Изменение URL (fallback)
    let currentUrl = window.location.href
    const checkUrlChange = () => {
      if (window.location.href !== currentUrl) {
        currentUrl = window.location.href
        closeDrawerOnNavigation()
      }
    }
    const urlCheckInterval = setInterval(checkUrlChange, 100)

    return () => {
      // Очистка всех обработчиков
      window.removeEventListener('astro:before-preparation', closeDrawerOnNavigation)
      window.removeEventListener('astro:after-swap', closeDrawerOnNavigation)
      window.removeEventListener('swup:contentReplaced', closeDrawerOnNavigation)
      window.removeEventListener('swup:pageView', closeDrawerOnNavigation)
      window.removeEventListener('popstate', closeDrawerOnNavigation)

      history.pushState = origPush
      history.replaceState = origReplace
      clearInterval(urlCheckInterval)
    }
  }, [])

  return (
    <>
      {/* Пометка о том, что сайт в разработке */}
      <div className='bg-rose-400 text-white text-center font-bold py-1 sm:py-2 text-xs sm:text-sm sticky top-0 z-50'>
        Сайт в разработке. Демонстрационный контент.
      </div>
      <header
        // className={`sticky top-0 z-50 w-full transition-all duration-300 ${
        //   scrolled ? 'bg-olive-500/20 border-olive-200/50 border-b backdrop-blur-md' : 'bg-transparent'
        // }`}
        className='sticky top-[40px] z-40 w-full transition-all duration-300 bg-gradient-to-r from-[#8BC34A]/10 via-white/40 to-[#8BC34A]/10 backdrop-blur-md border-b border-[#8BC34A]/30'
      >
        <div className='flex h-20 items-center justify-between px-4 md:px-6'>
          <a href='/' className='flex items-center gap-2'>
            <img src='/17a70891-5433-419e-99aa-33350107e7c9-removebg-preview.png' alt='Stom-Line' className='h-16 sm:h-20 md:h-24' />
            <span className='bg-gradient-to-r from-[#4E8C29] to-[#8BC34A] bg-clip-text text-transparent text-lg sm:text-xl font-bold hidden xs:inline'>Stom-Line</span>
          </a>

          <nav className="hidden md:flex md:gap-8">
            {navItems.map((item) => (
              <a
                key={item.title}
                href={item.href}
                className="group relative text-sm font-medium text-[#4E8C29] transition-all duration-300 hover:text-[#8BC34A] hover:scale-105"
              >
                {item.title}
                <span className="absolute -bottom-1 left-0 h-[2px] w-0 bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] transition-all duration-300 group-hover:w-full shadow-sm" />
              </a>
            ))}
          </nav>

          <div className="flex flex-wrap items-center gap-2">
            {/* Переключатель версии для слабовидящих */}
            <AccessibilityToggle />

            {/* Компонент поиска для десктопа */}
            <Search buttonVariant="ghost" buttonSize="icon" />

            {/* Кнопка заказа звонка для десктопа */}
            <div className="hidden md:block">
              <Dialog open={callbackDialogOpen} onOpenChange={setCallbackDialogOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant="default"
                    size="sm"
                    className="bg-gradient-to-r from-[#8BC34A] to-[#4E8C29] hover:from-[#4E8C29] hover:to-[#85C026] text-white shadow-lg transition-all duration-300 hover:scale-105"
                  >
                    <Phone className="mr-2 h-4 w-4" />
                    Заказать звонок
                  </Button>
                </DialogTrigger>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle className="text-[#4E8C29]">Заказать обратный звонок</DialogTitle>
                    <DialogDescription>
                      Оставьте заявку и мы свяжемся с вами в течение 15 минут
                    </DialogDescription>
                  </DialogHeader>
                  <CallbackForm
                    variant="compact"
                    onSuccess={() => setCallbackDialogOpen(false)}
                  />
                </DialogContent>
              </Dialog>
            </div>

            {/* Мобильное меню */}
            <div className="md:hidden">
              <Drawer open={drawerOpen} onOpenChange={setDrawerOpen}>
                <DrawerTrigger asChild>
                  <Button variant="ghost" size="icon" className="inline-flex items-center justify-center rounded-full bg-gradient-to-br from-[#8BC34A]/20 to-[#85C026]/20 p-2 text-[#4E8C29] backdrop-blur-sm hover:from-[#8BC34A]/30 hover:to-[#85C026]/30 hover:text-[#8BC34A] focus:outline-none shadow-lg transition-all duration-300 hover:scale-105">
                    <Menu className="h-6 w-6" />
                  </Button>
                </DrawerTrigger>
                <DrawerContent className="bg-white max-h-[85vh] flex flex-col">
                  <DrawerHeader className="flex-shrink-0 pb-2">
                    <DrawerTitle className="text-[#4E8C29] text-lg font-semibold">Меню</DrawerTitle>
                  </DrawerHeader>

                  {/* Поиск и доступность в мобильном меню */}
                  <div className="px-4 pb-3 space-y-2 flex-shrink-0">
                    <DrawerClose asChild>
                      <Button
                        variant="outline"
                        className="w-full justify-start text-[#4E8C29] border-[#8BC34A]/30 h-11 min-h-[44px]"
                        onClick={() => {
                          // После закрытия drawer запускаем событие для открытия поиска
                          setTimeout(() => {
                            window.dispatchEvent(new Event('openSearchDialog'))
                          }, 100);
                        }}
                      >
                        <SearchIcon className="mr-2 h-4 w-4" />
                        Поиск по сайту
                      </Button>
                    </DrawerClose>

                    {/* Переключатель доступности в мобильном меню */}
                    <div className="w-full flex justify-center py-1">
                      <AccessibilityToggle />
                    </div>
                  </div>

                  {/* Навигация в мобильном меню - с прокруткой */}
                  <div className="flex-1 overflow-y-auto px-4 pb-2">
                    <div className="space-y-1">
                      {navItems.map((item) => (
                        <DrawerClose asChild key={item.title}>
                          <a
                            href={item.href}
                            className="text-[#4E8C29] hover:bg-[#8BC34A]/20 hover:text-[#4E8C29] flex items-center rounded-lg px-3 py-2.5 text-sm font-medium transition-colors min-h-[44px]"
                          >
                            {item.title}
                          </a>
                        </DrawerClose>
                      ))}
                    </div>
                  </div>

                  <DrawerFooter className="flex-shrink-0 pt-2">
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-[#4E8C29] text-center">Заказать обратный звонок</h3>
                      <CallbackForm
                        variant="compact"
                        onSuccess={() => setDrawerOpen(false)}
                      />
                    </div>
                  </DrawerFooter>
                </DrawerContent>
              </Drawer>
            </div>
          </div>
        </div>
      </header>
    </>
  )
}
