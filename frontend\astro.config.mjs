// @ts-check
import { defineConfig } from 'astro/config'
import tailwindcss from '@tailwindcss/vite'

import react from '@astrojs/react'

import node from '@astrojs/node'
import swup from '@swup/astro'

// https://astro.build/config
export default defineConfig({
  server: {
    host: '0.0.0.0',
    port: 4321
  },

  vite: {
    plugins: [tailwindcss()]
  },

  integrations: [react(), swup()],
  output: 'server',

  adapter: node({
    mode: 'standalone'
  }),

  plugins: []
})
