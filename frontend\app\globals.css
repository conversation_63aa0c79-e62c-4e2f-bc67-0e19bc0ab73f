@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 82 30% 98%;
    --foreground: 82 30% 20%;

    --card: 0 0% 100%;
    --card-foreground: 82 30% 20%;

    --popover: 0 0% 100%;
    --popover-foreground: 82 30% 20%;

    --primary: 82 32% 40%;
    --primary-foreground: 0 0% 98%;

    --secondary: 82 32% 90%;
    --secondary-foreground: 82 32% 30%;

    --muted: 82 32% 96%;
    --muted-foreground: 82 5% 40%;

    --accent: 82 32% 96%;
    --accent-foreground: 82 32% 30%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;

    --border: 82 5.9% 90%;
    --input: 82 5.9% 90%;
    --ring: 82 32% 40%;

    --radius: 0.75rem;
  }

  .dark {
    --background: 82 32% 10%;
    --foreground: 0 0% 98%;

    --card: 82 32% 15%;
    --card-foreground: 0 0% 98%;

    --popover: 82 32% 15%;
    --popover-foreground: 0 0% 98%;

    --primary: 82 32% 40%;
    --primary-foreground: 0 0% 98%;

    --secondary: 82 32% 20%;
    --secondary-foreground: 0 0% 98%;

    --muted: 82 32% 20%;
    --muted-foreground: 82 5% 65%;

    --accent: 82 32% 25%;
    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;

    --border: 82 32% 20%;
    --input: 82 32% 20%;
    --ring: 82 32% 40%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

@layer utilities {
  .animate-sway {
    animation: sway 3s ease-in-out infinite alternate;
  }

  .animate-spin-slow {
    animation: spin 15s linear infinite;
  }

  .animate-spin-slow-reverse {
    animation: spin 20s linear infinite reverse;
  }
}

@keyframes sway {
  0% {
    transform: translateX(-1px) rotate(-2deg);
  }
  100% {
    transform: translateX(1px) rotate(2deg);
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes gradientMove {
  0% {
    background-position: 0% 0%;
  }
  100% {
    background-position: 200% 0%;
  }
}

