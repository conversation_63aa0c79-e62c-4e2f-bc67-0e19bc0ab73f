---
import Layout from '@/layouts/Layout.astro';
import { FilesManager } from '@/components/admin/FilesManager';
import { isUserAuthenticated } from '@/middleware/auth';

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);

if (!isAuthenticated) {
  return Astro.redirect('/admin/login');
}
---

<Layout 
  title="Документы и сертификаты - Админ-панель"
  description="Управление документами, лицензиями и сертификатами врачей"
  noindex={true}
>
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto px-4 py-8">
      <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900 mb-2">
          Документы и сертификаты
        </h1>
        <p class="text-gray-600">
          Управление документами организации, лицензиями и сертификатами врачей
        </p>
      </div>

      <FilesManager
        pbUrl="https://pb.stom-line.ru"
        client:only="react"
      />
    </div>
  </div>
</Layout>
