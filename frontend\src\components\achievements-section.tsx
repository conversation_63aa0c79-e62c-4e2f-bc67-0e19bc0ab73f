import { useState, useEffect, useRef } from "react"
import { X, Award, BadgeIcon as Certificate, ChevronLeft, ChevronRight, Users, Shield, Heart } from "lucide-react"
import { Button } from "@/components/ui/button"
import { EditButton } from "@/components/admin/EditButton"
import type { HtmlBlocks } from "@/lib/api"

import type React from "react"

// Компонент для безопасного рендеринга HTML
interface HtmlContentProps {
  content: string;
  className?: string;
}

const HtmlContent: React.FC<HtmlContentProps> = ({ content, className = "" }) => {
  return (
    <div
      className={className}
      dangerouslySetInnerHTML={{ __html: content }}
    />
  );
};

interface Achievement {
  id: number
  title: string
  description: string
  image: string
  type: "certificate" | "award"
  year: string
}

const achievements: Achievement[] = [
  {
    id: 1,
    title: "Сертификат качества ISO 9001",
    description: "Международный стандарт системы менеджмента качества",
    image: "/placeholder.svg?height=400&width=300",
    type: "certificate",
    year: "2023",
  },
  {
    id: 2,
    title: "Лучшая стоматологическая клиника года",
    description: "Награда от Ассоциации стоматологов России",
    image: "/placeholder.svg?height=400&width=300",
    type: "award",
    year: "2023",
  },
  {
    id: 3,
    title: "Сертификат инновационных технологий",
    description: "За внедрение современных методов лечения",
    image: "/placeholder.svg?height=400&width=300",
    type: "certificate",
    year: "2022",
  },
  {
    id: 4,
    title: "Премия за качество обслуживания",
    description: "Высшая оценка от пациентов и коллег",
    image: "/placeholder.svg?height=400&width=300",
    type: "award",
    year: "2022",
  },
  {
    id: 5,
    title: "Сертификат безопасности",
    description: "Соответствие всем стандартам безопасности",
    image: "/placeholder.svg?height=400&width=300",
    type: "certificate",
    year: "2023",
  },
  {
    id: 6,
    title: "Награда за инновации",
    description: "За использование передовых технологий",
    image: "/placeholder.svg?height=400&width=300",
    type: "award",
    year: "2021",
  },
]

interface AchievementsSectionProps {
  htmlBlocks?: HtmlBlocks[];
  isAuthenticated?: boolean;
}



// Custom hook for intersection observer
const useIntersectionObserver = (options = {}) => {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
      },
      {
        threshold: 0.1,
        rootMargin: "50px",
        ...options,
      },
    )

    if (ref.current) {
      observer.observe(ref.current)
    }

    return () => observer.disconnect()
  }, [options])

  return [ref, isIntersecting] as const
}

// Counter animation hook
const useCountUp = (end: number, duration = 2000, isVisible = false) => {
  const [count, setCount] = useState(0)

  useEffect(() => {
    if (!isVisible) return

    let startTime: number
    const startCount = 0

    const updateCount = (timestamp: number) => {
      if (!startTime) startTime = timestamp
      const progress = Math.min((timestamp - startTime) / duration, 1)

      setCount(Math.floor(progress * (end - startCount) + startCount))

      if (progress < 1) {
        requestAnimationFrame(updateCount)
      }
    }

    requestAnimationFrame(updateCount)
  }, [end, duration, isVisible])

  return count
}

export default function AchievementsSection({ htmlBlocks = [], isAuthenticated = false }: AchievementsSectionProps) {
  const [selectedAchievement, setSelectedAchievement] = useState<Achievement | null>(null)
  const [currentIndex, setCurrentIndex] = useState(0)

  // Функция для получения контента блока по ключу
  const getBlockContent = (key: string, fallback: string = '') => {

    return renderBlockContent(key, fallback)
    const block = htmlBlocks.find(b => b.key === key);
    return block?.content || fallback;
  }

  // Функция для рендеринга контента блока (поддерживает HTML)
  const renderBlockContent = (key: string, fallback: string = '', className?: string) => {
    const block = htmlBlocks.find(b => b.key === key);
    const content = block?.content || fallback;

    return <HtmlContent content={content} className={className} />;

    // Если блок имеет тип 'html', рендерим как HTML
    if (block?.type === 'html') {
      return <HtmlContent content={content} className={className} />;
    }

    // Иначе рендерим как обычный текст
    return <span className={className}>{content}</span>;
  }

  // Функция для получения ID блока по ключу
  const getBlockId = (key: string): string | undefined => {
    const block = htmlBlocks.find(b => b.key === key);
    return (block as any)?.id; // Приводим к any, так как id наследуется от BaseRecord
  }

  // Безопасная функция для рендера EditButton
  const renderEditButton = (key: string, options: {
    position?: 'top-right' | 'bottom-right' | 'inline';
    variant?: 'icon' | 'text';
    size?: 'sm' | 'md';
    className?: string;
    allowedFields?: string[];
  } = {}) => {
    const blockId = getBlockId(key);

    if (!isAuthenticated || !blockId) {
      return null;
    }

    return (
      <EditButton
        collection="html_blocks"
        id={blockId}
        position={options.position || 'top-right'}
        variant={options.variant || 'icon'}
        size={options.size || 'sm'}
        className={options.className || 'bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'}
        isAuthenticated={isAuthenticated}
        useModal={true}
        showQuickFields={true}
        allowedFields={options.allowedFields || ['content']}
      />
    );
  }

  // Функция для получения блоков по секции
  const getBlocksBySection = (section: string) => {
    return htmlBlocks.filter(b => b.section === section);
  }

  // Получаем контент для header секции - теперь используем renderBlockContent напрямую

  // Получаем контент для статистики
  const statsYears = getBlockContent('achievements_stats_years', '16+');
  const statsYearsLabel = getBlockContent('achievements_stats_years_label', 'Лет опыта');
  const statsPatients = getBlockContent('achievements_stats_patients', '2500+');
  const statsPatientsLabel = getBlockContent('achievements_stats_patients_label', 'Пациентов в год');
  const statsCertificates = getBlockContent('achievements_stats_certificates', '25+');
  const statsCertificatesLabel = getBlockContent('achievements_stats_certificates_label', 'Сертификатов');
  const statsAwards = getBlockContent('achievements_stats_awards', '10+');
  const statsAwardsLabel = getBlockContent('achievements_stats_awards_label', 'Наград');

  // Получаем контент для about секции - теперь используем renderBlockContent напрямую

  // Intersection observer refs
  const [headerRef, headerVisible] = useIntersectionObserver()
  const [statsRef, statsVisible] = useIntersectionObserver()
  const [aboutRef, aboutVisible] = useIntersectionObserver()
  const [processRef, processVisible] = useIntersectionObserver()
  const [teamRef, teamVisible] = useIntersectionObserver()
  const [achievementsRef, achievementsVisible] = useIntersectionObserver()

  // Counter animations - теперь используем статический контент из PocketBase
  // const yearsCount = useCountUp(16, 2000, statsVisible)
  // const patientsCount = useCountUp(2500, 2500, statsVisible)
  // const certificatesCount = useCountUp(25, 1500, statsVisible)
  // const awardsCount = useCountUp(10, 1000, statsVisible)

  const openModal = (achievement: Achievement) => {
    setSelectedAchievement(achievement)
    setCurrentIndex(achievements.findIndex((a) => a.id === achievement.id))
  }

  const closeModal = () => {
    setSelectedAchievement(null)
  }

  const navigateModal = (direction: "prev" | "next") => {
    const newIndex =
      direction === "prev"
        ? (currentIndex - 1 + achievements.length) % achievements.length
        : (currentIndex + 1) % achievements.length

    setCurrentIndex(newIndex)
    setSelectedAchievement(achievements[newIndex])
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Escape") closeModal()
    if (e.key === "ArrowLeft") navigateModal("prev")
    if (e.key === "ArrowRight") navigateModal("next")
  }

  return (
    <section className="py-16 px-4 bg-gradient-to-b from-olive-50 to-white overflow-hidden">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div
          ref={headerRef}
          className={`text-center mb-12 transition-all duration-1000 ease-out relative ${
            headerVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          }`}
        >
          {/* Edit button for header section */}
          {renderEditButton('achievements_header_badge', {
            position: 'top-right',
            variant: 'text',
            size: 'sm'
          })}

          <div className="w-full">
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-olive-100 to-olive-50 text-[#4E8C29] px-4 py-2 rounded-full text-sm font-medium mb-4 border border-olive-200 shadow-sm animate-pulse-subtle relative">
              <Award className="w-4 h-4 animate-bounce-gentle" />
              {renderBlockContent('achievements_header_badge', 'О клинике «Стомлайн»')}
            </div>
          </div>

          <div className="relative">
            <div className="flex flex-col text-3xl sm:text-4xl font-bold tracking-tighter text-[#4E8C29] mb-4 animate-fade-in-up">
            </div>
            {renderEditButton('achievements_header_title', {
              className: 'bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors absolute -top-2 -right-2'
            })}
          </div>

          <div className="relative inline-block">
            <div className="max-w-3xl mx-auto text-base sm:text-lg text-[#4E8C29] md:text-xl leading-relaxed animate-fade-in-up animation-delay-200">
              {renderBlockContent('achievements_header_description', 'Стоматологическая клиника «Стомлайн» работает в Мурманске с 2008 года. Каждый год мы принимаем порядка 2,5 тысяч пациентов, оказывая квалифицированную стоматологическую помощь даже в самых сложных, запущенных случаях.')}
            </div>
            {renderEditButton('achievements_header_description', {
              className: 'bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors absolute -top-2 -right-2'
            })}
          </div>
        </div>

        {/* Statistics */}
        <div
          ref={statsRef}
          className={`grid grid-cols-2 md:grid-cols-4 gap-6 mb-16 transition-all duration-1000 ease-out ${
            statsVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          }`}
        >
          <div className="group text-center p-6 bg-white rounded-2xl shadow-sm border border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-lg hover:shadow-olive-100/50 hover:-translate-y-1 relative overflow-hidden">
            {renderEditButton('achievements_stats_years', {
              className: 'absolute top-2 right-2 z-20 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
            })}
            <div className="absolute inset-0 bg-gradient-to-br from-olive-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className="text-3xl font-bold text-[#4E8C29] mb-2 animate-pulse-number">{statsYears}</div>
              <div className="text-gray-600 text-sm relative">
                {statsYearsLabel}
                {renderEditButton('achievements_stats_years_label', {
                  className: 'absolute -top-1 -right-1 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-xs'
                })}
              </div>
            </div>
          </div>

          <div className="group text-center p-6 bg-white rounded-2xl shadow-sm border border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-lg hover:shadow-olive-100/50 hover:-translate-y-1 relative overflow-hidden animation-delay-100">
            {renderEditButton('achievements_stats_patients', {
              className: 'absolute top-2 right-2 z-20 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
            })}
            <div className="absolute inset-0 bg-gradient-to-br from-olive-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className="text-3xl font-bold text-[#4E8C29] mb-2 animate-pulse-number">{statsPatients}</div>
              <div className="text-gray-600 text-sm relative">
                {statsPatientsLabel}
                {renderEditButton('achievements_stats_patients_label', {
                  className: 'absolute -top-1 -right-1 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-xs'
                })}
              </div>
            </div>
          </div>

          <div className="group text-center p-6 bg-white rounded-2xl shadow-sm border border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-lg hover:shadow-olive-100/50 hover:-translate-y-1 relative overflow-hidden animation-delay-200">
            {renderEditButton('achievements_stats_certificates', {
              className: 'absolute top-2 right-2 z-20 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
            })}
            <div className="absolute inset-0 bg-gradient-to-br from-olive-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className="text-3xl font-bold text-[#4E8C29] mb-2 animate-pulse-number">{statsCertificates}</div>
              <div className="text-gray-600 text-sm relative">
                {statsCertificatesLabel}
                {renderEditButton('achievements_stats_certificates_label', {
                  className: 'absolute -top-1 -right-1 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-xs'
                })}
              </div>
            </div>
          </div>

          <div className="group text-center p-6 bg-white rounded-2xl shadow-sm border border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-lg hover:shadow-olive-100/50 hover:-translate-y-1 relative overflow-hidden animation-delay-300">
            {renderEditButton('achievements_stats_awards', {
              className: 'absolute top-2 right-2 z-20 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
            })}
            <div className="absolute inset-0 bg-gradient-to-br from-olive-50/50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
            <div className="relative z-10">
              <div className="text-3xl font-bold text-[#4E8C29] mb-2 animate-pulse-number">{statsAwards}</div>
              <div className="text-gray-600 text-sm relative">
                {statsAwardsLabel}
                {renderEditButton('achievements_stats_awards_label', {
                  className: 'absolute -top-1 -right-1 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-xs'
                })}
              </div>
            </div>
          </div>
        </div>

        {/* About Section */}
        <div
          ref={aboutRef}
          className={`mb-16 transition-all duration-1000 ease-out ${
            aboutVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          }`}
        >
          <div className="grid md:grid-cols-2 gap-8 mb-12">
            <div className="group bg-white rounded-2xl p-8 shadow-sm border border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-xl hover:shadow-olive-100/30 hover:-translate-y-2 relative overflow-hidden">
              {renderEditButton('achievements_about_team_title', {
                className: 'absolute top-2 right-2 z-20 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
              })}
              <div className="absolute inset-0 bg-gradient-to-br from-olive-50/30 to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -top-10 -right-10 w-20 h-20 bg-olive-100 rounded-full opacity-20 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-olive-100 to-olive-200 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-inner">
                    <Users className="w-6 h-6 text-[#4E8C29] group-hover:animate-pulse" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-[#4E8C29] transition-colors duration-300">
                    {renderBlockContent('achievements_about_team_title', 'Профессиональная команда')}
                  </h3>
                </div>
                <div className="text-gray-600 leading-relaxed relative">
                  {renderBlockContent('achievements_about_team_description', 'Гордостью нашей клиники является сработанная команда квалифицированных врачей, постоянно стремящихся к новым знаниям. Они повышают свой профессионализм, посещая семинары, курсы, конференции и выставки, следят за новыми методиками и идут в ногу со временем.')}
                  {renderEditButton('achievements_about_team_description', {
                    position: 'bottom-right',
                    className: 'absolute -bottom-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
                  })}
                </div>
              </div>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-sm border border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-xl hover:shadow-olive-100/30 hover:-translate-y-2 relative overflow-hidden animation-delay-100">
              {renderEditButton('achievements_about_equipment_title', {
                className: 'absolute top-2 right-2 z-20 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
              })}
              <div className="absolute inset-0 bg-gradient-to-br from-olive-50/30 to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -top-10 -right-10 w-20 h-20 bg-olive-100 rounded-full opacity-20 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-olive-100 to-olive-200 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-inner">
                    <Award className="w-6 h-6 text-[#4E8C29] group-hover:animate-pulse" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-[#4E8C29] transition-colors duration-300">
                    {renderBlockContent('achievements_about_equipment_title', 'Современное оборудование')}
                  </h3>
                </div>
                <div className="text-gray-600 leading-relaxed relative">
                  {renderBlockContent('achievements_about_equipment_description', 'Наша клиника оснащена уникальным оборудованием и применяет передовые технологии лечения и восстановления зубов. Протезные конструкции изготавливаются в собственной зуботехнической лаборатории, что позволяет нашим пациентам экономить время и деньги.')}
                  {renderEditButton('achievements_about_equipment_description', {
                    position: 'bottom-right',
                    className: 'absolute -bottom-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
                  })}
                </div>
              </div>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-sm border border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-xl hover:shadow-olive-100/30 hover:-translate-y-2 relative overflow-hidden animation-delay-200">
              {renderEditButton('achievements_about_safety_title', {
                className: 'absolute top-2 right-2 z-20 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
              })}
              <div className="absolute inset-0 bg-gradient-to-br from-olive-50/30 to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -top-10 -right-10 w-20 h-20 bg-olive-100 rounded-full opacity-20 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-olive-100 to-olive-200 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-inner">
                    <Shield className="w-6 h-6 text-[#4E8C29] group-hover:animate-pulse" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-[#4E8C29] transition-colors duration-300">
                    {renderBlockContent('achievements_about_safety_title', 'Безопасность и комфорт')}
                  </h3>
                </div>
                <div className="text-gray-600 leading-relaxed relative">
                  {renderBlockContent('achievements_about_safety_description', 'Обеспечение максимального комфорта и полной инфекционной безопасности – основные приоритеты нашей работы. Все лечебные процедуры проводятся в 4 руки, то есть врачу помогает ассистент. Они используют только сертифицированные материалы и стерильный инструментарий.')}
                  {renderEditButton('achievements_about_safety_description', {
                    position: 'bottom-right',
                    className: 'absolute -bottom-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
                  })}
                </div>
              </div>
            </div>

            <div className="group bg-white rounded-2xl p-8 shadow-sm border border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-xl hover:shadow-olive-100/30 hover:-translate-y-2 relative overflow-hidden animation-delay-300">
              {renderEditButton('achievements_about_philosophy_title', {
                className: 'absolute top-2 right-2 z-20 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
              })}
              <div className="absolute inset-0 bg-gradient-to-br from-olive-50/30 to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              <div className="absolute -top-10 -right-10 w-20 h-20 bg-olive-100 rounded-full opacity-20 group-hover:scale-150 transition-transform duration-700"></div>
              <div className="relative z-10">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-olive-100 to-olive-200 rounded-xl flex items-center justify-center group-hover:scale-110 transition-transform duration-300 shadow-inner">
                    <Heart className="w-6 h-6 text-[#4E8C29] group-hover:animate-pulse" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 group-hover:text-[#4E8C29] transition-colors duration-300">
                    {renderBlockContent('achievements_about_philosophy_title', 'Наша философия')}
                  </h3>
                </div>
                <div className="text-gray-600 leading-relaxed relative">
                  {renderBlockContent('achievements_about_philosophy_description', '«Стомлайн» – команда высокопрофессиональных единомышленников, на практике реализующих концепции профилактической, микроинвазивной и эстетической стоматологии. Мы оказываем комплекс высококачественных стоматологических услуг по доступным ценам.')}
                  {renderEditButton('achievements_about_philosophy_description', {
                    position: 'bottom-right',
                    className: 'absolute -bottom-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
                  })}
                </div>
              </div>
            </div>
          </div>

          {/* Mission Statement */}
          <div className="bg-gradient-to-r from-[#4E8C29] via-[#8BC34A] to-[#4E8C29] rounded-2xl p-8 md:p-12 text-white mb-12 relative overflow-hidden animate-gradient-bg bg-300%">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
            <div className="relative z-10">
              <div className="relative inline-block mx-auto">
                <h3 className="text-xl sm:text-2xl md:text-3xl font-bold tracking-tighter mb-6 text-center animate-fade-in-up">
                  {renderBlockContent('achievements_mission_title', 'Наши принципы работы')}
                </h3>
                {renderEditButton('achievements_mission_title', {
                  className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-gray-700'
                })}
              </div>
              <div className="grid md:grid-cols-2 gap-8">
                <div className="animate-fade-in-up animation-delay-100 relative">
                  <div className="text-olive-100 leading-relaxed mb-4">
                    {renderBlockContent('achievements_mission_description_1', 'Мы используем только современное оборудование, передовые технологии, методики и материалы – это наш принцип. Наши специалисты молоды, целеустремленны и профессиональны. Они имеют немалый опыт практической работы, высокий уровень подготовки.')}
                  </div>
                  {renderEditButton('achievements_mission_description_1', {
                    className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-gray-700'
                  })}
                </div>
                <div className="animate-fade-in-up animation-delay-200 relative">
                  <div className="text-olive-100 leading-relaxed">
                    {renderBlockContent('achievements_mission_description_2', 'Наши специалисты не прекращают повышать свою квалификацию. Мы хотим стать одними из лучших – и мы имеем для этого все необходимое!')}
                  </div>
                  {renderEditButton('achievements_mission_description_2', {
                    className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-gray-700'
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* How We Work Section */}
        <div
          ref={processRef}
          className={`mb-16 transition-all duration-1000 ease-out ${
            processVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          }`}
        >
          <div className="relative inline-block mx-auto">
            <h3 className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tighter text-[#4E8C29] text-center mb-8 animate-fade-in-up">
              {renderBlockContent('achievements_process_title', 'Как мы работаем?')}
            </h3>
            {renderEditButton('achievements_process_title', {
              className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
            })}
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[1, 2, 3, 4, 5].map((step, index) => {
              const stepTitle = getBlockContent(`achievements_process_step${step}_title`, `Этап ${step}`);
              const stepDescription = getBlockContent(`achievements_process_step${step}_description`, `Описание этапа ${step}`);

              return (
                <div
                  key={step}
                  className={`group bg-white rounded-2xl p-6 shadow-sm border-2 border-olive-100 hover:border-olive-300 transition-all duration-500 hover:shadow-xl hover:shadow-olive-100/30 hover:-translate-y-2 relative overflow-hidden ${
                    index >= 3 ? "md:col-span-2 lg:col-span-1" : ""
                  } ${index === 4 ? "md:col-span-2 lg:col-span-2" : ""}`}
                  style={{ animationDelay: `${index * 100}ms` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-olive-50/50 to-blue-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute -top-5 -right-5 w-16 h-16 bg-olive-100 rounded-full opacity-20 group-hover:scale-125 transition-transform duration-500"></div>
                  <div className="relative z-10">
                    <div className="w-12 h-12 bg-gradient-to-br from-olive-100 to-olive-200 rounded-xl flex items-center justify-center mb-4 mx-auto group-hover:scale-110 transition-transform duration-300 shadow-lg border border-olive-200">
                      <span className="text-[#4E8C29] font-bold text-xl group-hover:animate-pulse">{step}</span>
                    </div>
                    <div className="relative">
                      <h4 className="font-semibold text-gray-900 mb-3 text-center group-hover:text-[#4E8C29] transition-colors duration-300">
                        {stepTitle}
                      </h4>
                      {renderEditButton(`achievements_process_step${step}_title`, {
                        position: 'top-right',
                        className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
                      })}
                    </div>
                    <div className="relative">
                      <p className="text-gray-600 text-sm leading-relaxed">{stepDescription}</p>
                      {renderEditButton(`achievements_process_step${step}_description`, {
                        position: 'bottom-right',
                        className: 'absolute -bottom-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
                      })}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Team Section */}
        <div
          ref={teamRef}
          className={`mb-16 transition-all duration-1000 ease-out ${
            teamVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          }`}
        >
          <div className="relative inline-block mx-auto">
            <h3 className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tighter text-[#4E8C29] text-center mb-8 animate-fade-in-up">
              {renderBlockContent('achievements_team_title', 'Персонал клиники «Стомлайн»')}
            </h3>
            {renderEditButton('achievements_team_title', {
              className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
            })}
          </div>
          <div className="grid md:grid-cols-2 gap-6 max-w-4xl mx-auto">
            {[1, 2].map((memberNum, index) => {
              const memberName = getBlockContent(`achievements_team_member${memberNum}_name`, `Сотрудник ${memberNum}`);
              const memberPosition = getBlockContent(`achievements_team_member${memberNum}_position`, `Должность ${memberNum}`);

              return (
                <div
                  key={memberNum}
                  className="group bg-white rounded-2xl p-6 shadow-sm border-2 border-olive-100 hover:border-olive-300 text-center transition-all duration-500 hover:shadow-xl hover:shadow-olive-100/30 hover:-translate-y-2 relative overflow-hidden"
                  style={{ animationDelay: `${index * 150}ms` }}
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-olive-50/50 to-blue-50/30 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute -top-8 -right-8 w-20 h-20 bg-olive-100 rounded-full opacity-20 group-hover:scale-150 transition-transform duration-700"></div>
                  <div className="relative z-10">
                    <div className="w-20 h-20 bg-gradient-to-br from-olive-100 to-olive-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:scale-110 transition-transform duration-300 shadow-lg border-2 border-olive-200">
                      <Users className="w-10 h-10 text-[#4E8C29] group-hover:animate-pulse" />
                    </div>
                    <div className="relative">
                      <h4 className="font-semibold text-gray-900 mb-2 group-hover:text-[#4E8C29] transition-colors duration-300">
                        {memberName}
                      </h4>
                      {renderEditButton(`achievements_team_member${memberNum}_name`, {
                        position: 'top-right',
                        className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
                      })}
                    </div>
                    <div className="relative">
                      <p className="text-[#4E8C29] text-sm font-medium">{memberPosition}</p>
                      {renderEditButton(`achievements_team_member${memberNum}_position`, {
                        position: 'bottom-right',
                        className: 'absolute -bottom-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
                      })}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Achievements Grid */}
        <div
          ref={achievementsRef}
          className={`mb-16 transition-all duration-1000 ease-out ${
            achievementsVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-8"
          }`}
        >
          <div className="relative inline-block mx-auto">
            <h3 className="text-2xl sm:text-3xl md:text-4xl font-bold tracking-tighter text-[#4E8C29] text-center mb-8 animate-fade-in-up">
              {renderBlockContent('achievements_gallery_title', 'Наши достижения')}
            </h3>
            {renderEditButton('achievements_gallery_title', {
              className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors'
            })}
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {achievements.map((achievement, index) => (
              <div
                key={achievement.id}
                className="group bg-white rounded-2xl shadow-sm border-2 border-olive-100 hover:border-olive-300 overflow-hidden hover:shadow-xl hover:shadow-olive-100/30 transition-all duration-500 cursor-pointer hover:-translate-y-2 relative"
                onClick={() => openModal(achievement)}
                role="button"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === "Enter" || e.key === " ") {
                    e.preventDefault()
                    openModal(achievement)
                  }
                }}
                aria-label={`Открыть ${achievement.title}`}
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="absolute inset-0 bg-gradient-to-br from-olive-50/30 to-blue-50/20 opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                <div className="relative aspect-[4/3] overflow-hidden">
                  <img
                    src={achievement.image || "/placeholder.svg"}
                    alt={achievement.title}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-700"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
                  <div className="absolute top-4 left-4">
                    <span
                      className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-xs font-medium backdrop-blur-sm border transition-all duration-300 ${
                        achievement.type === "certificate"
                          ? "bg-blue-100/90 text-blue-800 border-blue-200 group-hover:bg-blue-200/90"
                          : "bg-yellow-100/90 text-yellow-800 border-yellow-200 group-hover:bg-yellow-200/90"
                      }`}
                    >
                      {achievement.type === "certificate" ? (
                        <Certificate className="w-3 h-3 group-hover:animate-pulse" />
                      ) : (
                        <Award className="w-3 h-3 group-hover:animate-pulse" />
                      )}
                      {achievement.type === "certificate" ? "Сертификат" : "Награда"}
                    </span>
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className="bg-white/90 backdrop-blur-sm px-2 py-1 rounded-full text-xs font-medium text-gray-700 border border-white/50 group-hover:bg-white group-hover:scale-105 transition-all duration-300">
                      {achievement.year}
                    </span>
                  </div>
                </div>
                <div className="p-6 relative z-10">
                  <h4 className="font-semibold text-gray-900 mb-2 group-hover:text-green-600 transition-colors duration-300">
                    {achievement.title}
                  </h4>
                  <p className="text-gray-600 text-sm leading-relaxed">{achievement.description}</p>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Call to Action */}
        <div className="text-center animate-fade-in-up">
          <div className="bg-gradient-to-r from-[#4E8C29] via-[#8BC34A] to-[#4E8C29] text-white rounded-2xl p-8 md:p-12 relative overflow-hidden animate-gradient-bg bg-300%">
            <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
            <div className="relative z-10">
              <div className="relative inline-block">
                <h3 className="text-xl sm:text-2xl md:text-3xl font-bold tracking-tighter mb-4">
                  {renderBlockContent('achievements_cta_title', 'Доверьте свое здоровье профессионалам')}
                </h3>
                {renderEditButton('achievements_cta_title', {
                  className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-gray-700'
                })}
              </div>
              <div className="relative inline-block">
                <div className="text-olive-100 mb-6 max-w-2xl mx-auto">
                  {renderBlockContent('achievements_cta_description', 'Наши достижения — это результат многолетней работы и стремления к совершенству. Запишитесь на консультацию уже сегодня!')}
                </div>
                {renderEditButton('achievements_cta_description', {
                  className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-gray-700'
                })}
              </div>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <div className="relative inline-block">
                  <Button className="group bg-white text-[#4E8C29] px-8 py-3 rounded-xl font-semibold hover:bg-olive-50 transition-all duration-300 hover:scale-105 hover:shadow-lg border-2 border-transparent hover:border-olive-200">
                    <span className="group-hover:animate-pulse">
                      {renderBlockContent('achievements_cta_button_primary', 'Записаться на прием')}
                    </span>
                  </Button>
                  {renderEditButton('achievements_cta_button_primary', {
                    className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-gray-700'
                  })}
                </div>
                <div className="relative inline-block">
                  <Button variant="outline" className="group border-2 border-white text-white px-8 py-3 rounded-xl font-semibold hover:bg-white hover:text-[#4E8C29] transition-all duration-300 hover:scale-105 hover:shadow-lg">
                    <span className="group-hover:animate-pulse">
                      {renderBlockContent('achievements_cta_button_secondary', 'Узнать больше')}
                    </span>
                  </Button>
                  {renderEditButton('achievements_cta_button_secondary', {
                    className: 'absolute -top-2 -right-2 bg-white/90 border border-gray-200 shadow-sm hover:bg-olive-50 transition-colors text-gray-700'
                  })}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Modal */}
      {selectedAchievement && (
        <div
          className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4 animate-fade-in"
          onClick={closeModal}
          onKeyDown={handleKeyDown}
          tabIndex={-1}
        >
          <div
            className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden animate-scale-in border-2 border-olive-200 shadow-2xl"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="relative">
              <Button
                onClick={closeModal}
                variant="ghost"
                size="icon"
                className="absolute top-4 right-4 z-10 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-300 hover:scale-110 border border-gray-200 hover:border-olive-300"
                aria-label="Закрыть модальное окно"
              >
                <X className="w-5 h-5 hover:text-[#4E8C29] transition-colors duration-300" />
              </Button>

              <Button
                onClick={() => navigateModal("prev")}
                variant="ghost"
                size="icon"
                className="absolute left-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-300 hover:scale-110 border border-gray-200 hover:border-olive-300"
                aria-label="Предыдущее достижение"
              >
                <ChevronLeft className="w-5 h-5 hover:text-[#4E8C29] transition-colors duration-300" />
              </Button>

              <Button
                onClick={() => navigateModal("next")}
                variant="ghost"
                size="icon"
                className="absolute right-4 top-1/2 -translate-y-1/2 z-10 bg-white/90 backdrop-blur-sm rounded-full hover:bg-white transition-all duration-300 hover:scale-110 border border-gray-200 hover:border-olive-300"
                aria-label="Следующее достижение"
              >
                <ChevronRight className="w-5 h-5 hover:text-[#4E8C29] transition-colors duration-300" />
              </Button>

              <div className="grid md:grid-cols-2 gap-0">
                <div className="relative aspect-[4/3] md:aspect-auto">
                  <img
                    src={selectedAchievement.image || "/placeholder.svg"}
                    alt={selectedAchievement.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <div className="p-8">
                  <div className="flex items-center gap-2 mb-4">
                    <span
                      className={`inline-flex items-center gap-1 px-3 py-1 rounded-full text-sm font-medium border ${
                        selectedAchievement.type === "certificate"
                          ? "bg-blue-100 text-blue-800 border-blue-200"
                          : "bg-yellow-100 text-yellow-800 border-yellow-200"
                      }`}
                    >
                      {selectedAchievement.type === "certificate" ? (
                        <Certificate className="w-4 h-4" />
                      ) : (
                        <Award className="w-4 h-4" />
                      )}
                      {selectedAchievement.type === "certificate" ? "Сертификат" : "Награда"}
                    </span>
                    <span className="text-gray-500 text-sm bg-gray-100 px-2 py-1 rounded-full">
                      {selectedAchievement.year}
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold text-gray-900 mb-4">{selectedAchievement.title}</h3>
                  <p className="text-gray-600 leading-relaxed mb-6">{selectedAchievement.description}</p>
                  <div className="text-sm text-gray-500 bg-gray-50 px-3 py-2 rounded-lg inline-block">
                    {currentIndex + 1} из {achievements.length}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </section>
  )
}
