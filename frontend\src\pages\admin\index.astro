---
import Layout from '@/layouts/Layout.astro';
import { AdminDashboard } from '@/components/admin/AdminDashboard';
import { isUserAuthenticated } from '@/middleware/auth';

// Проверяем авторизацию через middleware
const isAuthenticated = isUserAuthenticated(Astro.locals);
---

<Layout 
  title="Панель администратора - Стом-Лайн"
  description="Система управления контентом стоматологической клиники Стом-Лайн"
  noindex={true}
>
  <AdminDashboard 
    isAuthenticated={isAuthenticated}
    pbUrl="https://pb.stom-line.ru"
    client:only="react"
  />
</Layout>
