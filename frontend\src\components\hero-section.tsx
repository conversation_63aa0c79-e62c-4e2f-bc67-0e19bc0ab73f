'use client'

import { useEffect, useRef, useState } from 'react'
import { motion } from 'framer-motion'
import { AuroraText } from './magicui/aurora-text'
import { CallbackForm } from './callback-form'
import type { HtmlBlocks } from '@/lib/api'
import VKWidget from './vk-widget'

interface HeroSectionProps {
  htmlBlocks?: HtmlBlocks[]
}

export default function HeroSection({ htmlBlocks = [] }: HeroSectionProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const [isVisible, setIsVisible] = useState(false)

  // Функция для получения контента блока по ключу
  const getBlockContent = (key: string, fallback: string = '') => {
    const block = htmlBlocks.find((b) => b.key === key)
    return block?.content || fallback
  }

  // Получаем контент для различных элементов
  const badge = getBlockContent('hero_badge', 'Премиальная стоматология')
  const title = getBlockContent('hero_title', 'Стоматология')
  const titleAccent = getBlockContent('hero_title_accent', 'Будущего')
  const description = getBlockContent(
    'hero_description',
    'Современные технологии лечения и протезирования. Индивидуальный подход к каждому пациенту. Забота о вашей улыбке с 2010 года.'
  )

  useEffect(() => {
    setIsVisible(true)
    
    // Добавляем обработчик скролла для параллакс-эффекта
    const handleScroll = () => {
      const scrolled = window.scrollY
      const parallaxElements = document.querySelectorAll('.parallax-element')
      
      parallaxElements.forEach((element) => {
        const speed = parseFloat(element.getAttribute('data-speed') || '0.5')
        const yPos = -(scrolled * speed)
        ;(element as HTMLElement).style.transform = `translateY(${yPos}px)`
      })
    }
    
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext('2d')
    if (!ctx) return

    canvas.width = canvas.offsetWidth
    canvas.height = canvas.offsetHeight

    const particles: Particle[] = []
    const particleCount = 50

    class Particle {
      x: number
      y: number
      size: number
      speedX: number
      speedY: number
      color: string

      constructor() {
        this.x = Math.random() * canvas.width
        this.y = Math.random() * canvas.height
        this.size = Math.random() * 3 + 1
        this.speedX = Math.random() * 0.5 - 0.25
        this.speedY = Math.random() * 0.5 - 0.25
        this.color = `rgba(127, 134, 67, ${Math.random() * 0.3 + 0.1})`
      }

      update() {
        this.x += this.speedX
        this.y += this.speedY

        if (this.x > canvas.width) this.x = 0
        else if (this.x < 0) this.x = canvas.width

        if (this.y > canvas.height) this.y = 0
        else if (this.y < 0) this.y = canvas.height
      }

      draw() {
        ctx.fillStyle = this.color
        ctx.beginPath()
        ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2)
        ctx.fill()
      }
    }

    for (let i = 0; i < particleCount; i++) {
      particles.push(new Particle())
    }

    function connectParticles() {
      for (let i = 0; i < particles.length; i++) {
        for (let j = i; j < particles.length; j++) {
          const dx = particles[i].x - particles[j].x
          const dy = particles[i].y - particles[j].y
          const distance = Math.sqrt(dx * dx + dy * dy)

          if (distance < 100) {
            ctx.beginPath()
            ctx.strokeStyle = `rgba(127, 134, 67, ${0.1 - distance / 1000})`
            ctx.lineWidth = 0.5
            ctx.moveTo(particles[i].x, particles[i].y)
            ctx.lineTo(particles[j].x, particles[j].y)
            ctx.stroke()
          }
        }
      }
    }

    function animate() {
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      for (const particle of particles) {
        particle.update()
        particle.draw()
      }

      connectParticles()
      requestAnimationFrame(animate)
    }

    animate()

    const handleResize = () => {
      canvas.width = canvas.offsetWidth
      canvas.height = canvas.offsetHeight
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <section className='relative min-h-[80vh] overflow-hidden py-12 sm:min-h-[90vh] sm:py-20 md:py-32'>
      <style>{`
        @keyframes gradient-border {
          0%, 100% { border-radius: 37% 29% 27% 27% / 28% 25% 41% 37%; }
          25% { border-radius: 47% 29% 39% 49% / 61% 19% 66% 26%; }
          50% { border-radius: 57% 23% 47% 72% / 63% 17% 66% 33%; }
          75% { border-radius: 28% 49% 29% 100% / 93% 20% 64% 25%; }
        }
        @keyframes gradient-1 {
          0%, 100% { top: 0; right: 0; }
          50% { top: 50%; right: 25%; }
          75% { top: 25%; right: 50%; }
        }
        @keyframes gradient-2 {
          0%, 100% { top: 0; left: 0; }
          60% { top: 75%; left: 25%; }
          85% { top: 50%; left: 50%; }
        }
        @keyframes gradient-3 {
          0%, 100% { bottom: 0; left: 0; }
          40% { bottom: 50%; left: 25%; }
          65% { bottom: 25%; left: 50%; }
        }
        @keyframes gradient-4 {
          0%, 100% { bottom: 0; right: 0; }
          50% { bottom: 25%; right: 40%; }
          90% { bottom: 50%; right: 25%; }
        }
        @keyframes float {
          0%, 100% { transform: translateY(0px); }
          50% { transform: translateY(-10px); }
        }
        @keyframes pulse-glow {
          0%, 100% { box-shadow: 0 0 20px rgba(139, 195, 74, 0.3); }
          50% { box-shadow: 0 0 40px rgba(139, 195, 74, 0.6); }
        }
        @keyframes sparkle {
          0%, 100% { opacity: 0; transform: scale(0) rotate(0deg); }
          50% { opacity: 1; transform: scale(1) rotate(180deg); }
        }
        @keyframes shimmer {
          0% { background-position: -200% 0; }
          100% { background-position: 200% 0; }
        }
        .bg-gradient-radial {
          background: radial-gradient(circle, var(--tw-gradient-stops));
        }
      `}</style>
      <canvas ref={canvasRef} className='absolute inset-0 z-0 h-full w-full'></canvas>
      <div className='relative z-10 container mx-auto px-4 md:px-6'>
        {/* Мобильная версия с симметричной компоновкой */}
        <div className='flex flex-col gap-8 md:hidden'>
          {/* 1. Центральный заголовок с параллакс-эффектом */}
          <motion.div
            className='flex flex-col items-center space-y-4 text-center'
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
            transition={{ duration: 0.7, delay: 0.1 }}
          >
            <motion.div
              className='inline-flex items-center rounded-full border border-[#8BC34A]/30 bg-gradient-to-r from-[#8BC34A]/20 to-[#85C026]/20 px-4 py-2 text-xs font-medium text-gray-800 shadow-lg backdrop-blur-sm'
              initial={{ scale: 0.8, opacity: 0 }}
              animate={{ scale: 1, opacity: 1 }}
              transition={{ duration: 0.5, delay: 0.3 }}
              whileHover={{ scale: 1.05 }}
            >
              <span className='mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-[#8BC34A] to-[#4E8C29]'></span>
              {badge}
            </motion.div>
            
            <motion.div
              className='space-y-3'
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.7, delay: 0.4 }}
            >
              <h1 className='text-3xl font-bold tracking-tighter text-gray-900'>
                {title}{' '}
                <div className='mt-2 bg-gradient-to-r from-[#4E8C29] to-[#8BC34A] bg-clip-text text-transparent'>
                  <AuroraText speed={2}>{titleAccent}</AuroraText>
                </div>
              </h1>
              <p className='text-base text-gray-800 leading-relaxed'>{description}</p>
            </motion.div>
          </motion.div>

          {/* 2. Центральное изображение с wow-эффектами */}
          <motion.div
            className='flex items-center justify-center'
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: isVisible ? 1 : 0, scale: isVisible ? 1 : 0.9 }}
            transition={{ duration: 0.8, delay: 0.6, type: 'spring' }}
            whileHover={{ scale: 1.02 }}
          >
            <div className='relative aspect-square w-full max-w-[280px]'>
              <motion.div
                className='absolute inset-0 flex items-center justify-center overflow-hidden rounded-3xl border-2 border-[#8BC34A]/30 shadow-2xl backdrop-blur-sm'
                initial={{ scale: 0, rotate: -8 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 1, delay: 0.8, type: 'spring', stiffness: 120 }}
                style={{
                  animation: 'float 6s ease-in-out infinite, pulse-glow 4s ease-in-out infinite',
                  backgroundImage: 'linear-gradient(135deg, rgba(139, 195, 74, 0.1) 0%, rgba(133, 192, 38, 0.15) 50%, rgba(78, 140, 41, 0.1) 100%)',
                  opacity: 0.95
                }}
                whileHover={{ 
                  scale: 1.05,
                  rotate: 2,
                  transition: { duration: 0.3 }
                }}
              >
                {/* Основное изображение врачей */}
                <div className='relative h-full w-full overflow-hidden rounded-2xl'>
                  <img
                    src='/IMG-20250909-WA0008.jpg'
                    alt='Профессиональные врачи стоматологической клиники Stom Line'
                    className='h-full w-full object-cover'
                    loading='eager'
                  />
                  
                  {/* Градиентный оверлей */}
                  <div className='absolute inset-0 bg-gradient-to-t from-[#8BC34A]/40 via-transparent to-transparent'></div>
                  
                  {/* Светящаяся рамка */}
                  <div className='absolute inset-0 rounded-2xl border border-[#8BC34A]/50 shadow-inner'></div>
                </div>

                {/* Декоративные элементы */}
                <div className='pointer-events-none absolute inset-0 overflow-hidden rounded-2xl'>
                  {/* Анимированные градиентные пятна */}
                  <span className='pointer-events-none absolute -top-4 -left-4 h-20 w-20 animate-[gradient-1_8s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#8BC34A]/40 to-transparent blur-xl'></span>
                  <span className='pointer-events-none absolute -top-4 -right-4 h-20 w-20 animate-[gradient-2_10s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#85C026]/40 to-transparent blur-xl'></span>
                  <span className='pointer-events-none absolute -bottom-4 -left-4 h-20 w-20 animate-[gradient-3_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#4E8C29]/40 to-transparent blur-xl'></span>
                  
                  {/* Блестящие частицы */}
                  <span className='pointer-events-none absolute top-4 left-4 h-1.5 w-1.5 animate-pulse rounded-full bg-white/80 blur-sm'></span>
                  <span className='pointer-events-none absolute top-8 right-6 h-1 w-1 animate-pulse rounded-full bg-white/70 blur-sm' style={{animationDelay: '0.7s'}}></span>
                  <span className='pointer-events-none absolute bottom-6 left-8 h-1.5 w-1.5 animate-pulse rounded-full bg-white/90 blur-sm' style={{animationDelay: '1.2s'}}></span>
                </div>

                {/* Текст поверх изображения */}
                <div className='absolute bottom-3 left-3 right-3 text-center'>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.2 }}
                    className='rounded-lg bg-black/30 px-2 py-1 backdrop-blur-sm'
                  >
                    <p className='text-xs font-semibold text-white'>
                      Профессиональная команда
                    </p>
                  </motion.div>
                </div>
              </motion.div>
              {/* Улучшенные светящиеся эффекты */}
              <div className='absolute -bottom-4 -left-4 h-16 w-16 animate-pulse rounded-full bg-[#8BC34A]/60 blur-3xl sm:h-20 sm:w-20'></div>
              <div
                className='absolute -top-4 -right-4 h-16 w-16 animate-pulse rounded-full bg-[#85C026]/60 blur-3xl sm:h-20 sm:w-20'
                style={{ animationDelay: '1s' }}
              ></div>
              <div
                className='absolute top-1/2 left-1/2 h-28 w-28 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-[#4E8C29]/30 blur-3xl sm:h-32 sm:w-32'
                style={{ animationDelay: '0.5s' }}
              ></div>

              {/* Дополнительные акцентные точки */}
              <div
                className='absolute top-1/4 right-1/4 h-8 w-8 animate-pulse rounded-full bg-[#8BC34A]/50 blur-2xl sm:h-12 sm:w-12'
                style={{ animationDelay: '2s' }}
              ></div>
              <div
                className='absolute bottom-1/4 left-1/4 h-8 w-8 animate-pulse rounded-full bg-[#85C026]/50 blur-2xl sm:h-12 sm:w-12'
                style={{ animationDelay: '1.5s' }}
              ></div>
            </div>
          </motion.div>

          {/* 3. Форма обратной связи с анимацией появления */}
          <motion.div
            className='flex justify-center'
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: isVisible ? 1 : 0, y: isVisible ? 0 : 30 }}
            transition={{ duration: 0.7, delay: 1.0 }}
          >
            <CallbackForm
              variant="expanded"
              title="Запишитесь на консультацию"
              description="Оставьте заявку и мы свяжемся с вами в течение 15 минут"
              className="w-full max-w-[500px]"
            />
          </motion.div>
        </div>

        {/* Десктопная версия с симметричной компоновкой */}
        <div className='hidden md:grid md:grid-cols-2 md:gap-16 md:items-center'>
          {/* Левая колонка - текст и форма с параллакс-эффектом */}
          <div className='flex flex-col justify-center space-y-6 text-center md:text-left parallax-element' data-speed='0.2'>
            <motion.div
              className='mx-auto inline-flex items-center rounded-full border border-[#8BC34A]/30 bg-gradient-to-r from-[#8BC34A]/20 to-[#85C026]/20 px-4 py-2 text-xs font-medium text-gray-800 shadow-lg backdrop-blur-sm sm:text-sm md:mx-0'
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : -30 }}
              transition={{ duration: 0.6, delay: 0.2 }}
            >
              <span className='mr-2 block h-2 w-2 rounded-full bg-gradient-to-r from-[#8BC34A] to-[#4E8C29]'></span>
              {badge}
            </motion.div>
            
            <motion.div 
              className='space-y-4'
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : -30 }}
              transition={{ duration: 0.6, delay: 0.4 }}
              whileHover={{ 
                scale: 1.01,
                transition: { duration: 0.2 }
              }}
            >
              <h1 className='text-4xl font-bold tracking-tighter text-gray-900 md:text-5xl lg:text-6xl xl:text-7xl'>
                {title}{' '}
                <motion.div 
                  className='mt-3 bg-gradient-to-r from-[#4E8C29] to-[#8BC34A] bg-clip-text text-transparent'
                  whileHover={{ 
                    scale: 1.02,
                    transition: { duration: 0.3 }
                  }}
                >
                  <AuroraText speed={2}>{titleAccent}</AuroraText>
                </motion.div>
              </h1>
              <p className='mx-auto max-w-[500px] text-lg text-gray-800 md:mx-0 md:text-xl leading-relaxed'>
                {description}
              </p>
            </motion.div>
            
            <motion.div
              className='flex justify-center md:justify-start'
              initial={{ opacity: 0, x: -30 }}
              animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : -30 }}
              transition={{ duration: 0.6, delay: 0.6 }}
            >
              <CallbackForm
                variant="expanded"
                title="Запишитесь на консультацию"
                description="Оставьте заявку и мы свяжемся с вами в течение 15 минут"
                className="w-full max-w-[500px]"
              />
            </motion.div>
          </div>

          {/* Правая колонка - изображение с параллакс-эффектом */}
            <motion.div
              className='flex items-center justify-center parallax-element'
              data-speed='0.3'
              initial={{ opacity: 0, x: 30 }}
              animate={{ opacity: isVisible ? 1 : 0, x: isVisible ? 0 : 30 }}
              transition={{ duration: 0.8, ease: 'easeOut', delay: 0.3 }}
              whileHover={{ 
                scale: 1.02,
                transition: { duration: 0.3 }
              }}
            >
              <div className='relative aspect-square w-full max-w-[240px] sm:max-w-[320px] md:max-w-[400px]'>
              <motion.div
                className='absolute inset-0 flex items-center justify-center overflow-hidden rounded-3xl border-2 border-[#8BC34A]/30 shadow-2xl backdrop-blur-sm'
                initial={{ scale: 0, rotate: -5 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ duration: 1, delay: 0.3, type: 'spring', stiffness: 100 }}
                style={{
                  animation: 'float 4s ease-in-out infinite, pulse-glow 3s ease-in-out infinite',
                  backgroundImage: 'linear-gradient(135deg, rgba(139, 195, 74, 0.1) 0%, rgba(133, 192, 38, 0.15) 50%, rgba(78, 140, 41, 0.1) 100%)',
                  opacity: 0.95
                }}
              >
                {/* Основное изображение врачей */}
                <div className='relative h-full w-full overflow-hidden rounded-2xl'>
                  <img
                    src='/IMG-20250909-WA0008.jpg'
                    alt='Профессиональные врачи стоматологической клиники Stom Line'
                    className='h-full w-full object-cover'
                    loading='eager'
                  />
                  
                  {/* Градиентный оверлей */}
                  <div className='absolute inset-0 bg-gradient-to-t from-[#8BC34A]/40 via-transparent to-transparent'></div>
                  
                  {/* Светящаяся рамка */}
                  <div className='absolute inset-0 rounded-2xl border border-[#8BC34A]/50 shadow-inner'></div>
                </div>

                {/* Декоративные элементы */}
                <div className='pointer-events-none absolute inset-0 overflow-hidden rounded-2xl'>
                  {/* Анимированные градиентные пятна */}
                  <span className='pointer-events-none absolute -top-4 -left-4 h-32 w-32 animate-[gradient-1_8s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#8BC34A]/40 to-transparent blur-2xl'></span>
                  <span className='pointer-events-none absolute -top-4 -right-4 h-32 w-32 animate-[gradient-2_10s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#85C026]/40 to-transparent blur-2xl'></span>
                  <span className='pointer-events-none absolute -bottom-4 -left-4 h-32 w-32 animate-[gradient-3_12s_ease-in-out_infinite_alternate] rounded-full bg-gradient-to-br from-[#4E8C29]/40 to-transparent blur-2xl'></span>
                  
                  {/* Блестящие частицы */}
                  <span className='pointer-events-none absolute top-6 left-6 h-3 w-3 animate-pulse rounded-full bg-white/80 blur-sm'></span>
                  <span className='pointer-events-none absolute top-12 right-8 h-2 w-2 animate-pulse rounded-full bg-white/70 blur-sm' style={{animationDelay: '0.7s'}}></span>
                  <span className='pointer-events-none absolute bottom-8 left-10 h-2.5 w-2.5 animate-pulse rounded-full bg-white/90 blur-sm' style={{animationDelay: '1.2s'}}></span>
                </div>

                {/* Текст поверх изображения */}
                <div className='absolute bottom-4 left-4 right-4 text-center'>
                  <motion.div
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.8, delay: 1.2 }}
                    className='rounded-lg bg-black/30 px-3 py-2 backdrop-blur-sm'
                  >
                    <p className='text-xs font-semibold text-white sm:text-sm'>
                      Профессиональная команда
                    </p>
                  </motion.div>
                </div>
              </motion.div>
              {/* Улучшенные светящиеся эффекты */}
              <div className='absolute -bottom-4 -left-4 h-20 w-20 animate-pulse rounded-full bg-[#8BC34A]/60 blur-3xl sm:h-28 sm:w-28'></div>
              <div
                className='absolute -top-4 -right-4 h-20 w-20 animate-pulse rounded-full bg-[#85C026]/60 blur-3xl sm:h-28 sm:w-28'
                style={{ animationDelay: '1s' }}
              ></div>
              <div
                className='absolute top-1/2 left-1/2 h-36 w-36 -translate-x-1/2 -translate-y-1/2 animate-pulse rounded-full bg-[#4E8C29]/30 blur-3xl sm:h-44 sm:w-44'
                style={{ animationDelay: '0.5s' }}
              ></div>

              {/* Дополнительные акцентные точки */}
              <div
                className='absolute top-1/4 right-1/4 h-12 w-12 animate-pulse rounded-full bg-[#8BC34A]/50 blur-2xl sm:h-16 sm:w-16'
                style={{ animationDelay: '2s' }}
              ></div>
              <div
                className='absolute bottom-1/4 left-1/4 h-12 w-12 animate-pulse rounded-full bg-[#85C026]/50 blur-2xl sm:h-16 sm:w-16'
                style={{ animationDelay: '1.5s' }}
              ></div>
            </div>
          </motion.div>
        </div>
      </div>
    </section>
  )
}
