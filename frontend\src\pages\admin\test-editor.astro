---
import Layout from '@/layouts/Layout.astro';
import { TestUniversalEditor } from '@/components/admin/test-universal-editor';
import { isUserAuthenticated } from '@/middleware/auth';

const isAuthenticated = isUserAuthenticated(Astro.locals);
if (!isAuthenticated) {
  return Astro.redirect('/admin');
}
---

<Layout title="Тестирование UniversalRecordEditor">
  <div class="min-h-screen bg-gray-50">
    <div class="container mx-auto py-8">
      <TestUniversalEditor client:only="react" />
    </div>
  </div>
</Layout>
