{"name": "astro-app", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "sync-search": "node scripts/sync-search.js", "init-search": "node scripts/sync-search.js init"}, "dependencies": {"@astrojs/mdx": "^4.3.0", "@astrojs/node": "^9.2.2", "@astrojs/react": "^4.3.0", "@docsearch/css": "^3.9.0", "@docsearch/react": "^3.9.0", "@meilisearch/instant-meilisearch": "^0.26.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@swup/astro": "^1.6.0", "@tailwindcss/vite": "^4.1.10", "@tiptap/extension-blockquote": "^2.14.0", "@tiptap/extension-code-block": "^2.14.0", "@tiptap/extension-collaboration": "^2.14.0", "@tiptap/extension-collaboration-cursor": "^2.14.0", "@tiptap/extension-color": "^2.14.0", "@tiptap/extension-highlight": "^2.14.0", "@tiptap/extension-horizontal-rule": "^2.14.0", "@tiptap/extension-image": "^2.14.0", "@tiptap/extension-link": "^2.14.0", "@tiptap/extension-mention": "^2.14.0", "@tiptap/extension-placeholder": "^2.14.0", "@tiptap/extension-subscript": "^2.14.0", "@tiptap/extension-superscript": "^2.14.0", "@tiptap/extension-table": "^2.14.0", "@tiptap/extension-table-cell": "^2.14.0", "@tiptap/extension-table-header": "^2.14.0", "@tiptap/extension-table-row": "^2.14.0", "@tiptap/extension-task-item": "^2.14.0", "@tiptap/extension-task-list": "^2.14.0", "@tiptap/extension-text-align": "^2.14.0", "@tiptap/extension-text-style": "^2.14.0", "@tiptap/extension-typography": "^2.14.0", "@tiptap/extension-underline": "^2.14.0", "@tiptap/pm": "^2.14.0", "@tiptap/react": "^2.14.0", "@tiptap/starter-kit": "^2.14.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "astro": "^5.9.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.18.1", "lucide-react": "^0.487.0", "motion": "^12.17.0", "pocketbase": "^0.26.1", "prettier-plugin-astro": "^0.14.1", "prettier-plugin-tailwindcss": "^0.6.12", "react": "^19.1.0", "react-dom": "^19.1.0", "react-intersection-observer": "^9.16.0", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.10", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.3.4", "vaul": "^1.1.2"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "jsdom": "^26.1.0", "vitest": "^3.2.4"}}