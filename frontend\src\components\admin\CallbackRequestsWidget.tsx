import * as React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Phone, Clock, CheckCircle, AlertTriangle, ExternalLink } from 'lucide-react';
import { getCollectionRecords } from '@/lib/pocketbase-admin';

interface CallbackRequest {
  id: string;
  name: string;
  phone: string;
  message?: string;
  isProcessed: boolean;
  created: string;
  ip_address?: string;
}

interface CallbackRequestsWidgetProps {
  pbUrl: string;
  maxItems?: number;
}

export const CallbackRequestsWidget: React.FC<CallbackRequestsWidgetProps> = ({
  pbUrl,
  maxItems = 5
}) => {
  const [requests, setRequests] = React.useState<CallbackRequest[]>([]);
  const [loading, setLoading] = React.useState(true);
  const [error, setError] = React.useState('');

  const loadRequests = React.useCallback(async () => {
    try {
      setLoading(true);
      const token = localStorage.getItem('pb_token');
      if (!token) return;

      const result = await getCollectionRecords(
        'callback_requests',
        token,
        1,
        maxItems,
        '-created' // Сортировка по дате создания (новые первые)
      );

      setRequests(result.items || []);
    } catch (err) {
      console.error('Ошибка загрузки заявок:', err);
      setError('Не удалось загрузить заявки');
    } finally {
      setLoading(false);
    }
  }, [maxItems]);

  React.useEffect(() => {
    loadRequests();
    
    // Автообновление каждые 30 секунд
    const interval = setInterval(loadRequests, 30000);
    return () => clearInterval(interval);
  }, [loadRequests]);

  const getTimeAgo = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    const diffHours = Math.floor(diffMins / 60);
    const diffDays = Math.floor(diffHours / 24);

    if (diffMins < 1) return 'только что';
    if (diffMins < 60) return `${diffMins} мин назад`;
    if (diffHours < 24) return `${diffHours} ч назад`;
    return `${diffDays} дн назад`;
  };

  const getUrgencyLevel = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));

    if (diffMins <= 15) return 'urgent'; // Критично - до 15 минут
    if (diffMins <= 60) return 'high'; // Высокий - до 1 часа
    if (diffMins <= 240) return 'medium'; // Средний - до 4 часов
    return 'low'; // Низкий - более 4 часов
  };

  const urgencyColors = {
    urgent: 'bg-red-100 text-red-800 border-red-200',
    high: 'bg-orange-100 text-orange-800 border-orange-200',
    medium: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    low: 'bg-gray-100 text-gray-800 border-gray-200'
  };

  const urgencyLabels = {
    urgent: 'СРОЧНО',
    high: 'Высокий',
    medium: 'Средний',
    low: 'Низкий'
  };

  const unprocessedRequests = requests.filter(req => !req.isProcessed);
  const recentRequests = requests.filter(req => {
    const diffMs = new Date().getTime() - new Date(req.created).getTime();
    return diffMs <= 24 * 60 * 60 * 1000; // За последние 24 часа
  });

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Phone className="mr-2 h-5 w-5" />
            Заявки на обратный звонок
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#8BC34A]"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center">
            <Phone className="mr-2 h-5 w-5" />
            Заявки на обратный звонок
          </div>
          <div className="flex items-center space-x-2">
            {unprocessedRequests.length > 0 && (
              <Badge variant="destructive" className="animate-pulse">
                {unprocessedRequests.length} новых
              </Badge>
            )}
            <Button
              variant="outline"
              size="sm"
              onClick={() => window.location.href = '/admin/callback_requests'}
            >
              <ExternalLink className="h-4 w-4 mr-1" />
              Все заявки
            </Button>
          </div>
        </CardTitle>
        <CardDescription>
          Последние заявки от клиентов • Обновлено {new Date().toLocaleTimeString('ru-RU')}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {error && (
          <div className="text-red-600 text-sm mb-4 p-3 bg-red-50 rounded-lg">
            {error}
          </div>
        )}

        {requests.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Phone className="mx-auto h-12 w-12 text-gray-300 mb-4" />
            <p>Заявок пока нет</p>
          </div>
        ) : (
          <div className="space-y-3">
            {/* Статистика */}
            <div className="grid grid-cols-3 gap-4 mb-4 p-3 bg-gray-50 rounded-lg">
              <div className="text-center">
                <div className="text-2xl font-bold text-red-600">{unprocessedRequests.length}</div>
                <div className="text-xs text-gray-600">Не обработано</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-blue-600">{recentRequests.length}</div>
                <div className="text-xs text-gray-600">За 24 часа</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold text-green-600">{requests.filter(r => r.isProcessed).length}</div>
                <div className="text-xs text-gray-600">Обработано</div>
              </div>
            </div>

            {/* Список заявок */}
            {requests.map((request) => {
              const urgency = getUrgencyLevel(request.created);
              const timeAgo = getTimeAgo(request.created);
              
              return (
                <div
                  key={request.id}
                  className={`p-3 rounded-lg border transition-all hover:shadow-md ${
                    !request.isProcessed ? 'bg-red-50 border-red-200' : 'bg-white border-gray-200'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="font-medium text-gray-900">{request.name}</span>
                        <Badge className={`text-xs ${urgencyColors[urgency]}`}>
                          {urgencyLabels[urgency]}
                        </Badge>
                        {request.isProcessed ? (
                          <CheckCircle className="h-4 w-4 text-green-500" />
                        ) : (
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        )}
                      </div>
                      
                      <div className="text-sm text-gray-600 mb-1">
                        <Phone className="inline h-3 w-3 mr-1" />
                        {request.phone}
                      </div>
                      
                      {request.message && (
                        <div className="text-sm text-gray-600 mb-2 italic">
                          "{request.message}"
                        </div>
                      )}
                      
                      <div className="flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        {timeAgo}
                      </div>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CardContent>
    </Card>
  );
};
